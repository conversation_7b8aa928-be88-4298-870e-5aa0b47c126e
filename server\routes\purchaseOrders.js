import express from 'express';
import PurchaseOrder from '../models/PurchaseOrder.js';
import Transaction from '../models/Transaction.js';
import { authenticateToken, requireAdmin, requireTrucker, requireAdminOrTrucker } from '../middleware/auth.js';

const router = express.Router();

// Get all purchase orders (admin can see all, truckers see only pending and their assigned)
router.get('/', authenticateToken, async (req, res) => {
  try {
    let query = {};

    if (req.user.role === 'trucker') {
      // Check if trucker has an active order (accepted but not delivered)
      const activeOrder = await PurchaseOrder.findOne({
        assignedTo: req.user._id,
        status: { $in: ['accepted', 'pickup_completed', 'en_route', 'delivery_in_progress'] }
      });

      if (activeOrder) {
        // If trucker has an active order, only show that order
        query = { _id: activeOrder._id };
      } else {
        // If no active order, show pending orders
        query = { status: 'pending' };
      }
    }
    // Admins can see all orders (no filter needed)

    const orders = await PurchaseOrder.find(query)
      .populate('createdBy', 'username firstName lastName')
      .populate('assignedTo', 'username firstName lastName')
      .sort({ createdAt: -1 });

    res.json({
      success: true,
      data: orders
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching purchase orders',
      error: error.message
    });
  }
});

// Get single purchase order
router.get('/:id', authenticateToken, async (req, res) => {
  try {
    const order = await PurchaseOrder.findById(req.params.id)
      .populate('createdBy', 'username firstName lastName')
      .populate('assignedTo', 'username firstName lastName');

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    // Check permissions
    if (req.user.role === 'trucker' && 
        order.status !== 'pending' && 
        !order.assignedTo?.equals(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'Access denied'
      });
    }

    res.json({
      success: true,
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error fetching purchase order',
      error: error.message
    });
  }
});

// Create new purchase order (admin only)
router.post('/', authenticateToken, requireAdmin, async (req, res) => {
  try {
    console.log('Received order data:', req.body);
    console.log('User:', req.user);

    // Validate required fields
    const { client, title, pickupLocation, deliveryLocations, pickupDate, deliveryDate } = req.body;

    if (!client || !title || !pickupLocation || !deliveryLocations || !Array.isArray(deliveryLocations) ||
        deliveryLocations.length === 0 || !pickupDate || !deliveryDate) {
      return res.status(400).json({
        success: false,
        message: 'Missing required fields',
        error: 'All required fields must be provided, including at least one delivery location with materials'
      });
    }

    // Transform the data to match new schema
    const orderData = {
      client: client.trim(),
      title: title.trim(),
      description: req.body.description?.trim() || '',
      pickupLocation: {
        address: pickupLocation,
        coordinates: { lat: 0, lng: 0 } // Will be set by pre-save middleware
      },
      deliveryLocations: deliveryLocations.map(location => ({
        address: location.address,
        coordinates: { lat: 0, lng: 0 }, // Will be set by pre-save middleware
        materials: location.materials.map(material => ({
          material: material.material,
          quantityOrdered: parseFloat(material.quantity),
          quantityDelivered: 0,
          unit: material.unit,
          ratePerUnit: parseFloat(material.ratePerUnit),
          isOverDelivered: false
        })),
        status: 'pending'
      })),
      pickupDate,
      deliveryDate,
      notes: req.body.notes?.trim() || '',
      createdBy: req.user._id
    };

    console.log('Creating order with data:', orderData);

    const order = new PurchaseOrder(orderData);
    await order.save();

    await order.populate('createdBy', 'username firstName lastName');

    console.log('Order created successfully:', order);

    res.status(201).json({
      success: true,
      message: 'Purchase order created successfully',
      data: order
    });
  } catch (error) {
    console.error('Error creating purchase order:', error);
    res.status(500).json({
      success: false,
      message: 'Error creating purchase order',
      error: error.message
    });
  }
});

// Accept pickup for delivery location (trucker only)
router.patch('/:id/accept-pickup/:deliveryId', authenticateToken, requireTrucker, async (req, res) => {
  try {
    const { latitude, longitude, pickupPhoto, pickupSignature, developerMode } = req.body;

    // Validate location data (unless in developer mode)
    if (!developerMode && (!latitude || !longitude)) {
      return res.status(400).json({
        success: false,
        message: 'Location data (latitude and longitude) is required to accept pickup'
      });
    }

    // Validate pickup documentation
    if (!pickupPhoto || !pickupSignature) {
      return res.status(400).json({
        success: false,
        message: 'Pickup photo and signature are required'
      });
    }

    // Check if trucker already has an active delivery
    const existingActiveOrder = await PurchaseOrder.findOne({
      'deliveryLocations.assignedTo': req.user._id,
      'deliveryLocations.status': { $in: ['pickup_accepted', 'pickup_completed', 'en_route'] }
    });

    if (existingActiveOrder) {
      return res.status(400).json({
        success: false,
        message: 'You already have an active delivery. Complete it before accepting a new one.'
      });
    }

    // Use atomic operation to prevent race conditions
    const order = await PurchaseOrder.findOneAndUpdate(
      {
        _id: req.params.id,
        'deliveryLocations._id': req.params.deliveryId,
        'deliveryLocations.$.status': 'pending' // Only update if delivery location is still pending
      },
      {
        'deliveryLocations.$.status': 'pickup_accepted',
        'deliveryLocations.$.assignedTo': req.user._id,
        'deliveryLocations.$.acceptedAt': new Date(),
        'deliveryLocations.$.pickupPhoto': pickupPhoto,
        'deliveryLocations.$.pickupSignature': pickupSignature
      },
      {
        new: true,
        runValidators: true
      }
    );

    if (!order) {
      // Either order doesn't exist or delivery location is no longer pending
      const existingOrder = await PurchaseOrder.findById(req.params.id);
      if (!existingOrder) {
        return res.status(404).json({
          success: false,
          message: 'Purchase order not found'
        });
      } else {
        const deliveryLocation = existingOrder.deliveryLocations.id(req.params.deliveryId);
        if (!deliveryLocation) {
          return res.status(404).json({
            success: false,
            message: 'Delivery location not found'
          });
        } else {
          return res.status(400).json({
            success: false,
            message: 'This delivery is no longer available for acceptance'
          });
        }
      }
    }

    // Check if trucker is within 1km of pickup location (skip in developer mode)
    if (!developerMode && latitude && longitude) {
      const isWithinGeofence = order.isWithinPickupGeofence(latitude, longitude, 2);

      if (!isWithinGeofence) {
        return res.status(400).json({
          success: false,
          message: 'You must be within 1km of the pickup location to accept this delivery'
        });
      }
    }

    // Create a partial transaction for the pickup
    const deliveryLocation = order.deliveryLocations.id(req.params.deliveryId);
    const transaction = new Transaction({
      purchaseOrderId: order._id,
      orderNumber: order.orderNumber,
      trucker: req.user._id,
      deliveryLocationId: req.params.deliveryId,
      deliveryAddress: deliveryLocation.address,
      materialsDelivered: deliveryLocation.materials.map(material => ({
        material: material.material,
        quantityDelivered: 0, // Will be updated when delivery is completed
        unit: material.unit,
        ratePerUnit: material.ratePerUnit
      })),
      pickupPhoto,
      pickupSignature,
      deliveryPhoto: null, // Will be added when delivery is completed
      signature: null, // Will be added when delivery is completed
      coordinates: {
        lat: latitude || 0,
        lng: longitude || 0
      },
      status: 'pickup_completed', // Partial transaction status
      pickupCompletedAt: new Date(),
      notes: ''
    });

    await transaction.save();

    await order.populate('deliveryLocations.assignedTo', 'username firstName lastName');

    res.json({
      success: true,
      message: 'Pickup accepted successfully! You can now proceed to the pickup location.',
      data: order,
      transaction: transaction
    });
  } catch (error) {
    console.error('Error accepting pickup:', error);
    res.status(500).json({
      success: false,
      message: 'Error accepting pickup',
      error: error.message
    });
  }
});

// Update purchase order status (admin only)
router.patch('/:id/status', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const { status } = req.body;
    const order = await PurchaseOrder.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    order.status = status;
    await order.save();

    await order.populate('createdBy', 'username firstName lastName');
    await order.populate('assignedTo', 'username firstName lastName');

    res.json({
      success: true,
      message: 'Purchase order status updated successfully',
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error updating purchase order status',
      error: error.message
    });
  }
});

// Complete pickup (trucker only) - for delivery location
router.patch('/:id/complete-pickup/:deliveryId', authenticateToken, requireTrucker, async (req, res) => {
  try {
    const order = await PurchaseOrder.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    // Find the specific delivery location
    const deliveryLocation = order.deliveryLocations.id(req.params.deliveryId);
    if (!deliveryLocation) {
      return res.status(404).json({
        success: false,
        message: 'Delivery location not found'
      });
    }

    if (!deliveryLocation.assignedTo?.equals(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'You are not assigned to this delivery location'
      });
    }

    if (deliveryLocation.status !== 'pickup_accepted') {
      return res.status(400).json({
        success: false,
        message: 'Delivery location must be in pickup_accepted status to complete pickup'
      });
    }

    // Update delivery location status
    deliveryLocation.status = 'pickup_completed';
    deliveryLocation.pickupCompletedAt = new Date();

    await order.save();

    res.json({
      success: true,
      message: 'Pickup completed successfully! You can now proceed to the delivery location.',
      data: order
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error completing pickup',
      error: error.message
    });
  }
});

// Complete delivery location (trucker only)
router.patch('/:id/complete-delivery/:deliveryId', authenticateToken, requireTrucker, async (req, res) => {
  try {
    const { latitude, longitude, deliveryPhoto, deliverySignature, actualQuantities, developerMode, notes } = req.body;

    // Validate required data (unless in developer mode)
    if (!developerMode && (!latitude || !longitude)) {
      return res.status(400).json({
        success: false,
        message: 'Location data is required'
      });
    }

    if (!deliveryPhoto || !deliverySignature) {
      return res.status(400).json({
        success: false,
        message: 'Delivery photo and delivery signature are required'
      });
    }

    if (!actualQuantities || !Array.isArray(actualQuantities)) {
      return res.status(400).json({
        success: false,
        message: 'Actual delivered quantities are required'
      });
    }

    const order = await PurchaseOrder.findById(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    // Find and validate the specific delivery location
    const deliveryLocation = order.deliveryLocations.id(req.params.deliveryId);
    if (!deliveryLocation) {
      return res.status(404).json({
        success: false,
        message: 'Delivery location not found'
      });
    }

    if (!deliveryLocation.assignedTo?.equals(req.user._id)) {
      return res.status(403).json({
        success: false,
        message: 'You are not assigned to this delivery location'
      });
    }

    if (deliveryLocation.status !== 'pickup_completed') {
      return res.status(400).json({
        success: false,
        message: 'Pickup must be completed before delivery can be completed'
      });
    }

    // Check if trucker is within geofence for this delivery location (skip in developer mode)
    if (!developerMode && latitude && longitude) {
      const isWithinGeofence = order.isWithinDeliveryGeofence(req.params.deliveryId, latitude, longitude, 2);

      if (!isWithinGeofence) {
        return res.status(400).json({
          success: false,
          message: 'You must be within 1km of the delivery location to complete delivery'
        });
      }
    }

    // Update the specific delivery location (already validated above)

    // Find the existing partial transaction for this delivery
    const transaction = await Transaction.findOne({
      deliveryLocationId: req.params.deliveryId,
      trucker: req.user._id,
      status: 'pickup_completed'
    });

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'No pickup transaction found for this delivery. Please accept pickup first.'
      });
    }

    // Update materials delivered based on actual quantities
    const materialsDelivered = deliveryLocation.materials.map((material, index) => {
      const actualQty = actualQuantities[index] ? parseFloat(actualQuantities[index]) : 0;
      return {
        material: material.material,
        quantityDelivered: actualQty,
        unit: material.unit,
        ratePerUnit: material.ratePerUnit
      };
    });

    // Update the transaction with delivery information
    transaction.materialsDelivered = materialsDelivered;
    transaction.deliveryPhoto = deliveryPhoto;
    transaction.deliverySignature = deliverySignature;
    transaction.status = 'completed';
    transaction.completedAt = new Date();
    transaction.notes = notes || '';
    transaction.coordinates = {
      lat: latitude || 0,
      lng: longitude || 0
    };

    await transaction.save();

    // Update material quantities in the delivery location
    let hasOverDelivery = false;
    for (let i = 0; i < deliveryLocation.materials.length; i++) {
      const material = deliveryLocation.materials[i];
      const actualQty = actualQuantities[i] ? parseFloat(actualQuantities[i]) : 0;

      material.quantityDelivered += actualQty;

      // Check for over-delivery
      if (material.quantityDelivered > material.quantityOrdered) {
        material.isOverDelivered = true;
        hasOverDelivery = true;
      }
    }

    // Check if all materials for this location are fully delivered
    const allMaterialsDelivered = deliveryLocation.materials.every(material =>
      material.quantityDelivered >= material.quantityOrdered
    );

    if (allMaterialsDelivered) {
      deliveryLocation.status = 'completed';
      deliveryLocation.completedAt = new Date();
    }

    // Check if all delivery locations are completed
    const allLocationsCompleted = order.deliveryLocations.every(loc => loc.status === 'completed');

    if (allLocationsCompleted) {
      order.status = 'delivered';
    } else {
      order.status = 'delivery_in_progress';
    }

    await order.save();

    res.json({
      success: true,
      message: allLocationsCompleted ? 'All deliveries completed! Order finished.' : 'Delivery completed successfully',
      data: order,
      transaction: transaction,
      warnings: hasOverDelivery ? ['Some materials were over-delivered'] : []
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error completing delivery',
      error: error.message
    });
  }
});

// Get transaction history for trucker (only completed transactions)
router.get('/transactions', authenticateToken, requireTrucker, async (req, res) => {
  try {
    const transactions = await Transaction.find({
      trucker: req.user._id,
      status: 'completed' // Only show completed transactions to truckers
    })
      .populate('trucker', 'username firstName lastName')
      .sort({ completedAt: -1 })
      .limit(50); // Limit to last 50 transactions

    // Ensure all transactions have valid data
    const validTransactions = transactions.filter(transaction => {
      if (!transaction.trucker) {
        console.warn(`Transaction ${transaction._id} has missing trucker reference`);
        return false;
      }
      return true;
    });

    res.json({
      success: true,
      data: validTransactions
    });
  } catch (error) {
    console.error('Error fetching trucker transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching transaction history',
      error: error.message
    });
  }
});

// Get all transactions for admin
router.get('/admin/transactions', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const transactions = await Transaction.find({})
      .populate('trucker', 'username firstName lastName')
      .populate('purchaseOrderId', 'orderNumber client title')
      .sort({ completedAt: -1 })
      .limit(100); // Limit to last 100 transactions

    // Filter out transactions with missing references and log them
    const validTransactions = transactions.filter(transaction => {
      if (!transaction.trucker) {
        console.warn(`Transaction ${transaction._id} has missing trucker reference`);
        return false;
      }
      if (!transaction.purchaseOrderId) {
        console.warn(`Transaction ${transaction._id} has missing purchaseOrderId reference`);
        // Still include these transactions but they'll be handled on frontend
        return true;
      }
      return true;
    });

    res.json({
      success: true,
      data: validTransactions
    });
  } catch (error) {
    console.error('Error fetching admin transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching transaction history',
      error: error.message
    });
  }
});

// Delete all transactions (admin only - for testing) - MUST come before single transaction delete
router.delete('/admin/transactions/delete-all', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const result = await Transaction.deleteMany({});

    res.json({
      success: true,
      message: `Successfully deleted ${result.deletedCount} transactions`,
      deletedCount: result.deletedCount
    });
  } catch (error) {
    console.error('Error deleting all transactions:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting all transactions',
      error: error.message
    });
  }
});

// Delete single transaction (admin only)
router.delete('/admin/transactions/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const transaction = await Transaction.findByIdAndDelete(req.params.id);

    if (!transaction) {
      return res.status(404).json({
        success: false,
        message: 'Transaction not found'
      });
    }

    res.json({
      success: true,
      message: 'Transaction deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting transaction:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting transaction',
      error: error.message
    });
  }
});

// Delete all purchase orders (admin only - for testing)
router.delete('/admin/delete-all', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const result = await PurchaseOrder.deleteMany({});

    res.json({
      success: true,
      message: `Successfully deleted ${result.deletedCount} purchase orders`,
      deletedCount: result.deletedCount
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error deleting all purchase orders',
      error: error.message
    });
  }
});

// Delete purchase order (admin only)
router.delete('/:id', authenticateToken, requireAdmin, async (req, res) => {
  try {
    const order = await PurchaseOrder.findByIdAndDelete(req.params.id);

    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    res.json({
      success: true,
      message: 'Purchase order deleted successfully'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Error deleting purchase order',
      error: error.message
    });
  }
});

export default router;
